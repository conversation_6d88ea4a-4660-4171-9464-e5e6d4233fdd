/**
 * Tests for BatchManager
 * Validates DOM operation batching and performance monitoring integration
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { BatchManager } from '../batch-manager';
import { PerformanceOperation, IPerformanceMonitor } from '../shared-performance-types';

// Import OperationPriority enum from the BatchManager file
// Since it's not exported, we'll define it here for testing
enum OperationPriority {
  CRITICAL = 0,
  HIGH = 1,
  NORMAL = 2,
  LOW = 3,
}

// Mock performance monitor
class MockPerformanceMonitor implements IPerformanceMonitor {
  public measureOperation = vi.fn().mockResolvedValue('mock-result');
  public measureOperationSync = vi.fn().mockImplementation((operation: any, fn: Function, metadata?: any) => {
    // Actually execute the function that's passed to it
    return fn();
  });
  public setPerformanceThreshold = vi.fn();
  public getPerformanceStats = vi.fn().mockReturnValue({
    operationCounts: new Map(),
    averageDurations: new Map(),
    benchmarkViolations: new Map(),
    memoryTrend: 'stable' as const
  });
}

describe('BatchManager', () => {
  let batchManager: BatchManager;
  let mockPerformanceMonitor: MockPerformanceMonitor;
  let rafCallbacks: FrameRequestCallback[] = [];
  let rafId = 0;

  // Helper function to trigger RAF callbacks
  const triggerRAF = () => {
    const callbacks = [...rafCallbacks];
    rafCallbacks.length = 0;
    callbacks.forEach(callback => callback(performance.now()));
  };

  beforeEach(() => {
    vi.useFakeTimers();

    // Reset RAF callbacks array
    rafCallbacks = [];
    rafId = 0;

    // Mock requestAnimationFrame to collect callbacks
    global.requestAnimationFrame = vi.fn().mockImplementation((callback: FrameRequestCallback) => {
      const id = ++rafId;
      rafCallbacks.push(callback);
      return id;
    });

    // Mock cancelAnimationFrame
    global.cancelAnimationFrame = vi.fn().mockImplementation((_id: number) => {
      // Remove callback from queue if it exists
      rafCallbacks.length = 0;
    });

    mockPerformanceMonitor = new MockPerformanceMonitor();
  });

  afterEach(() => {
    if (batchManager) {
      batchManager.destroy();
    }
    rafCallbacks = [];
    vi.useRealTimers();
    vi.restoreAllMocks();
  });

  describe('Constructor and Configuration', () => {
    it('should create batch manager with default options', () => {
      batchManager = new BatchManager();

      expect(batchManager).toBeDefined();
      expect(batchManager.getMetrics().totalReads).toBe(0);
      expect(batchManager.getMetrics().totalWrites).toBe(0);
    });

    it('should create batch manager with custom options', () => {
      batchManager = new BatchManager({
        enablePerfLogging: true,
        maxBatchTime: 32,
        usePriorityQueue: false,
        performanceMonitor: mockPerformanceMonitor
      });

      expect(batchManager).toBeDefined();
    });

    it('should apply default options correctly', () => {
      batchManager = new BatchManager({
        enablePerfLogging: true
      });

      // Should use defaults for unspecified options
      expect(batchManager).toBeDefined();
    });
  });

  describe('Read Operations', () => {
    beforeEach(() => {
      batchManager = new BatchManager({
        enablePerfLogging: true,
        performanceMonitor: mockPerformanceMonitor
      });
    });

    it('should schedule read operations', () => {
      const readCallback = vi.fn().mockReturnValue('read-result');

      batchManager.read('test-read', readCallback);

      expect(batchManager.getMetrics().totalReads).toBe(0); // Not executed yet
    });

    it('should execute read operations in batch', () => {
      const readCallback1 = vi.fn().mockReturnValue('result1');
      const readCallback2 = vi.fn().mockReturnValue('result2');

      batchManager.read('read1', readCallback1);
      batchManager.read('read2', readCallback2);

      // Advance timers to trigger requestAnimationFrame
      // Trigger the RAF callback
      triggerRAF();

      expect(readCallback1).toHaveBeenCalled();
      expect(readCallback2).toHaveBeenCalled();
      expect(batchManager.getMetrics().totalReads).toBe(2);
    });

    it('should handle read operation errors gracefully', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      const errorCallback = vi.fn().mockImplementation(() => {
        throw new Error('Read error');
      });

      batchManager.read('error-read', errorCallback);

      triggerRAF();

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('[BatchManager] Error in read operation'),
        expect.any(Error)
      );
      expect(batchManager.getMetrics().errorCount).toBe(1);

      consoleSpy.mockRestore();
    });

    it('should skip duplicate read operations', () => {
      const readCallback = vi.fn().mockReturnValue('result');

      batchManager.read('duplicate-read', readCallback);
      batchManager.read('duplicate-read', readCallback); // Same ID

      triggerRAF();

      expect(readCallback).toHaveBeenCalledTimes(1);
      // Note: The current implementation overwrites duplicates rather than skipping them
      // So we expect 0 skipped operations but only 1 execution
      expect(batchManager.getMetrics().totalReads).toBe(1);
    });
  });

  describe('Write Operations', () => {
    beforeEach(() => {
      batchManager = new BatchManager({
        enablePerfLogging: true,
        performanceMonitor: mockPerformanceMonitor
      });
    });

    it('should schedule write operations', () => {
      const writeCallback = vi.fn();

      batchManager.write('test-write', writeCallback);

      expect(batchManager.getMetrics().totalWrites).toBe(0); // Not executed yet
    });

    it('should execute write operations in batch', () => {
      const writeCallback1 = vi.fn();
      const writeCallback2 = vi.fn();

      batchManager.write('write1', writeCallback1);
      batchManager.write('write2', writeCallback2);

      triggerRAF();

      expect(writeCallback1).toHaveBeenCalled();
      expect(writeCallback2).toHaveBeenCalled();
      expect(batchManager.getMetrics().totalWrites).toBe(2);
    });

    it('should handle write operation errors gracefully', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      const errorCallback = vi.fn().mockImplementation(() => {
        throw new Error('Write error');
      });

      batchManager.write('error-write', errorCallback);

      triggerRAF();

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('[BatchManager] Error in write operation'),
        expect.any(Error)
      );
      expect(batchManager.getMetrics().errorCount).toBe(1);

      consoleSpy.mockRestore();
    });

    it('should skip duplicate write operations', () => {
      const writeCallback = vi.fn();

      batchManager.write('duplicate-write', writeCallback);
      batchManager.write('duplicate-write', writeCallback); // Same ID

      triggerRAF();

      expect(writeCallback).toHaveBeenCalledTimes(1);
      // Note: The current implementation overwrites duplicates rather than skipping them
      // So we expect 0 skipped operations but only 1 execution
      expect(batchManager.getMetrics().totalWrites).toBe(1);
    });
  });

  describe('Priority Queue Operations', () => {
    beforeEach(() => {
      batchManager = new BatchManager({
        usePriorityQueue: true,
        performanceMonitor: mockPerformanceMonitor
      });
    });

    it('should execute high priority operations first', () => {
      const executionOrder: string[] = [];

      const lowPriorityCallback = vi.fn().mockImplementation(() => {
        executionOrder.push('low');
      });
      const highPriorityCallback = vi.fn().mockImplementation(() => {
        executionOrder.push('high');
      });

      batchManager.read('low-priority', lowPriorityCallback, { priority: OperationPriority.LOW });
      batchManager.read('high-priority', highPriorityCallback, { priority: OperationPriority.HIGH });

      triggerRAF();

      expect(executionOrder).toEqual(['high', 'low']);
    });

    it('should handle operations with same priority in FIFO order', () => {
      const executionOrder: string[] = [];

      const callback1 = vi.fn().mockImplementation(() => {
        executionOrder.push('first');
      });
      const callback2 = vi.fn().mockImplementation(() => {
        executionOrder.push('second');
      });

      batchManager.read('first', callback1, { priority: OperationPriority.NORMAL });
      batchManager.read('second', callback2, { priority: OperationPriority.NORMAL });

      triggerRAF();

      expect(executionOrder).toEqual(['first', 'second']);
    });
  });

  describe('Performance Monitor Integration', () => {
    beforeEach(() => {
      batchManager = new BatchManager({
        enablePerfLogging: true,
        performanceMonitor: mockPerformanceMonitor
      });
    });

    it('should use performance monitor for batch operations', () => {
      const readCallback = vi.fn();
      const writeCallback = vi.fn();

      batchManager.read('test-read', readCallback);
      batchManager.write('test-write', writeCallback);

      triggerRAF();

      expect(mockPerformanceMonitor.measureOperationSync).toHaveBeenCalledWith(
        PerformanceOperation.DOM_BATCH,
        expect.any(Function),
        expect.objectContaining({
          elementCount: 2,
          operationType: 'batch_flush'
        })
      );
    });

    it('should work without performance monitor', () => {
      batchManager.destroy();
      batchManager = new BatchManager({
        enablePerfLogging: true
        // No performance monitor
      });

      const readCallback = vi.fn();
      batchManager.read('test-read', readCallback);

      triggerRAF();

      expect(readCallback).toHaveBeenCalled();
      // Should not throw errors
    });
  });

  describe('Batch Execution Order', () => {
    beforeEach(() => {
      batchManager = new BatchManager({
        performanceMonitor: mockPerformanceMonitor
      });
    });

    it('should execute reads before writes', () => {
      const executionOrder: string[] = [];

      const writeCallback = vi.fn().mockImplementation(() => {
        executionOrder.push('write');
      });
      const readCallback = vi.fn().mockImplementation(() => {
        executionOrder.push('read');
      });

      // Schedule write first, then read
      batchManager.write('test-write', writeCallback);
      batchManager.read('test-read', readCallback);

      triggerRAF();

      expect(executionOrder).toEqual(['read', 'write']);
    });

    it('should execute multiple reads before any writes', () => {
      const executionOrder: string[] = [];

      const read1 = vi.fn().mockImplementation(() => executionOrder.push('read1'));
      const read2 = vi.fn().mockImplementation(() => executionOrder.push('read2'));
      const write1 = vi.fn().mockImplementation(() => executionOrder.push('write1'));
      const write2 = vi.fn().mockImplementation(() => executionOrder.push('write2'));

      batchManager.write('write1', write1);
      batchManager.read('read1', read1);
      batchManager.write('write2', write2);
      batchManager.read('read2', read2);

      triggerRAF();

      expect(executionOrder).toEqual(['read1', 'read2', 'write1', 'write2']);
    });
  });

  describe('Performance Logging', () => {
    it('should log performance when enabled', () => {
      const consoleSpy = vi.spyOn(console, 'debug').mockImplementation(() => {});

      batchManager = new BatchManager({
        enablePerfLogging: true
      });

      // Mock performance.now to return predictable values
      const originalNow = performance.now;
      let callCount = 0;
      performance.now = vi.fn().mockImplementation(() => {
        callCount++;
        return callCount === 1 ? 0 : 10; // 10ms duration
      });

      const callback = vi.fn();
      batchManager.read('test', callback);

      triggerRAF();

      expect(batchManager.getMetrics().lastFlushTime).toBeGreaterThan(0);

      performance.now = originalNow;
      consoleSpy.mockRestore();
    });

    it('should not log performance when disabled', () => {
      batchManager = new BatchManager({
        enablePerfLogging: false
      });

      const callback = vi.fn();
      batchManager.read('test', callback);

      triggerRAF();

      expect(batchManager.getMetrics().lastFlushTime).toBe(0);
    });
  });

  describe('Resource Management', () => {
    it('should clean up resources on destroy', () => {
      batchManager = new BatchManager();

      const callback = vi.fn();
      batchManager.read('test', callback);

      batchManager.destroy();

      expect(global.cancelAnimationFrame).toHaveBeenCalled();
      expect(batchManager.getMetrics().totalReads).toBe(0);
      expect(batchManager.getMetrics().totalWrites).toBe(0);
    });

    it('should handle multiple destroy calls gracefully', () => {
      batchManager = new BatchManager();

      expect(() => {
        batchManager.destroy();
        batchManager.destroy();
      }).not.toThrow();
    });
  });

  describe('Metrics Tracking', () => {
    beforeEach(() => {
      batchManager = new BatchManager({
        enablePerfLogging: true
      });
    });

    it('should track operation counts correctly', () => {
      const readCallback = vi.fn();
      const writeCallback = vi.fn();

      batchManager.read('read1', readCallback);
      batchManager.read('read2', readCallback);
      batchManager.write('write1', writeCallback);

      triggerRAF();

      const metrics = batchManager.getMetrics();
      expect(metrics.totalReads).toBe(2);
      expect(metrics.totalWrites).toBe(1);
      expect(metrics.errorCount).toBe(0);
      expect(metrics.skippedOperations).toBe(0);
    });

    it('should track error counts', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const errorCallback = vi.fn().mockImplementation(() => {
        throw new Error('Test error');
      });

      batchManager.read('error1', errorCallback);
      batchManager.write('error2', errorCallback);

      triggerRAF();

      expect(batchManager.getMetrics().errorCount).toBe(2);

      consoleSpy.mockRestore();
    });
  });
});
